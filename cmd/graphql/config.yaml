# Gin-Vue-Admin Global Configuration

# jwt configuration
jwt:
  signing-key: {{ index . "JWT_SECRET" | default "qmPlus" }}
  expires-time: {{ index . "JWT_EXPIRES_TIME" | default "7d" }}
  buffer-time: {{ index . "JWT_BUFFER_TIME" | default "1d" }}
  issuer: {{ index . "JWT_ISSUER" | default "xbit-agent" }}

# admin configuration
admin:
  internal-api-key: {{ index . "INTERNAL_API_KEY" | default "default-internal-api-key-change-in-production" }}

# zap logger configuration
zap:
  level: info
  format: console
  prefix: '[xbit-agent]'
  director: log
  encode-level: LowercaseColorLevelEncoder
  stacktrace-key: stacktrace
  max-age: 0
  show-line: true
  log-in-console: true

# redis configuration
redis:
  addr: {{ index . "REDIS_HOST" | default "127.0.0.1" }}:{{ index . "REDIS_PORT" | default "6379" }}
  password: {{ index . "REDIS_PASS" | default "" }}
  db: {{ index . "REDIS_DB" | default "0" }}

# email configuration
email:
  to: <EMAIL>
  port: 465
  from: <EMAIL>
  host: smtp.163.com
  is-ssl: true
  secret: xxx
  nickname: test

# system configuration
system:
  env: {{ index . "APP_ENV" | default "local" }}
  addr: {{ index . "SERVER_PORT" | default "8080" }}
  db-type: pgsql
  oss-type: local
  use-multipoint: false
  use-redis: {{ index . "REDIS_HOST" | ne "" }}
  iplimit-count: {{ index . "RATE_LIMIT_COUNT" | default "15000" }}
  iplimit-time: {{ index . "RATE_LIMIT_TIME" | default "3600" }}
  #  Global route prefix
  router-prefix: "/api/dex-agent"
  graphql-prefix: "/api/dex-agent/graphql"
  admin-graphql-prefix: "/api/dex-agent/admin/graphql"
  # Level downgrade grace period configuration
  enable-level-downgrade: {{ index . "ENABLE_LEVEL_DOWNGRADE" | default "false" }}

# captcha configuration
captcha:
  key-long: 6
  img-width: 240
  img-height: 80
  open-captcha: 0
  open-captcha-timeout: 3600

# postgresql database configuration
pgsql:
  path: {{ index . "POSTGRES_AGENCY_HOST" | default "127.0.0.1" }}
  port: {{ index . "POSTGRES_AGENCY_PORT" | default "5432" }}
  config: sslmode={{ index . "POSTGRES_AGENCY_SSL_MODE" | default "disable" }}
  db-name: {{ index . "POSTGRES_DB" | default "agent" }}
  username: {{ index . "POSTGRES_AGENCY_USER" | default "postgres" }}
  password: {{ index . "POSTGRES_AGENCY_PASS" | default "postgres" }}
  prefix: ''
  singular: false
  engine: ''
  max-idle-conns: 10
  max-open-conns: 100
  log-mode: ''
  log-zap: false

# cors configuration
cors:
  mode: strict-whitelist
  whitelist:
    - allow-origin: example1.com
      allow-headers: Content-Type,AccessToken,X-CSRF-Token, Authorization, Token,X-Token,X-User-Id
      allow-methods: POST, GET
      expose-headers: Content-Length, Access-Control-Allow-Origin, Access-Control-Allow-Headers, Content-Type
      allow-credentials: true
    - allow-origin: example2.com
      allow-headers: content-type
      allow-methods: GET, POST
      expose-headers: Content-Length, Access-Control-Allow-Origin, Access-Control-Allow-Headers, Content-Type
      allow-credentials: true

db-list:
  - disable: false
    type: pgsql
    alias-name: system
    path: {{ index . "POSTGRES_AGENCY_HOST" | default "127.0.0.1" }}
    port: {{ index . "POSTGRES_AGENCY_PORT" | default "5432" }}
    config: sslmode={{ index . "POSTGRES_AGENCY_SSL_MODE" | default "disable" }}
    db-name: {{ index . "POSTGRES_DB" | default "agent" }}
    username: {{ index . "POSTGRES_AGENCY_USER" | default "postgres" }}
    password: {{ index . "POSTGRES_AGENCY_PASS" | default "postgres" }}
    max-idle-conns: 10
    max-open-conns: 100
    log-mode: ''
    log-zap: false

# local configuration
local:
  path: uploads/file
  store-path: uploads/file

# autocode configuration
autocode:
  web: web/src
  root: ""
  server: server
  module: 'gitlab.ggwp.life/xbit/xbit-dex/xbit-agent'
  ai-path: ""

# nats configuration (temporarily disabled - only using nats-meme for now)
# nats:
#   url: {{ index . "NATS_URL" | default "nats://127.0.0.1:4222" }}
#   token: {{ index . "NATS_TOKEN" | default "" }}
#   use-tls: {{ index . "NATS_USE_TLS" | default "false" }}
#   user: {{ index . "NATS_USER" | default "" }}
#   pass: {{ index . "NATS_PASS" | default "" }}

# nats-meme configuration (for activity cashback processing)
nats-meme:
  url: {{ index . "MEME_NATS_URL" | default "" }}
  user: {{ index . "MEME_NATS_USER" | default "" }}
  pass: {{ index . "MEME_NATS_PASS" | default "" }}
  use-tls: {{ index . "MEME_NATS_USE_TLS" | default "false" }}
  token: {{ index . "MEME_NATS_TOKEN" | default "" }}

# nats-dex configuration (temporarily disabled - not yet implemented for affiliate events)
nats-dex:
  url: {{ index . "NATS_URL" | default "" }}
  use-tls: {{ index . "NATS_USE_TLS" | default "false" }}
  user: {{ index . "NATS_USER" | default "" }}
  pass: {{ index . "NATS_PASS" | default "" }}

cron-tasks:
  - id: "referral_snapshot"
    cron: "*/5 * * * * *"
  - id: "level_upgrade"
    cron: "0 0 0 * * *"
  - id: "referral_tree_snapshot"
    #cron: "0 5 0 * * *"
    cron: "0 0 * * * *"
  - id: "infinite_agent_referral_tree"
    #cron: "0 5 0 * * *"
    cron: "0 0 * * * *"
  - id: "infinite_agent_commission"
    #cron: "0 5 0 * * *"
    cron: "0 0 * * * *"

hyper-liquid:
  system-launch-date: {{ index . "SYSTEM_LAUNCH_DATE" | default "" }}

turnkey:
  reward-wallet-sol: {{ index . "REWARD_WALLET_SOL" | default "CYbXfLbw2nzyjwi8WNFSuWNeiJkA9q21P9AdTjnejSC4" }}
  reward-wallet-evm: {{ index . "REWARD_WALLET_EVM" | default "******************************************" }}
  organization_id: {{ index . "ORGANIZATION_ID" | default "9c06aa58-738a-4b60-b178-6252b91024f8" }}