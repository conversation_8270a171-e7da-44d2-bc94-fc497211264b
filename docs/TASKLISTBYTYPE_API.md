# TaskListByType GraphQL API

## Overview

The `TaskListByType` GraphQL API allows clients to retrieve tasks filtered by task type (<PERSON><PERSON><PERSON><PERSON>, COMMUNITY, TRADING) along with the user's progress for each task. This API is designed to support task list screens that display tasks grouped by type.

## GraphQL Schema

### Query

```graphql
type Query {
  taskListByType(input: TaskListByTypeInput!): TaskListByTypeResponse! @auth
}
```

### Input Types

```graphql
input TaskListByTypeInput {
  taskType: TaskType!
}

enum TaskType {
  DAILY
  COMMUNITY
  TRADING
}
```

### Response Types

```graphql
type TaskListByTypeResponse {
  success: Boolean!
  message: String!
  data: [TaskWithProgress!]!
}

type TaskWithProgress {
  task: ActivityTask!
  progress: UserTaskProgress
}
```

## Usage Examples

### Query Daily Tasks

```graphql
query GetDailyTasks {
  taskListByType(input: { taskType: DAILY }) {
    success
    message
    data {
      task {
        id
        name
        description
        taskType
        frequency
        points
        isActive
        sortOrder
        category {
          id
          name
          displayName
        }
      }
      progress {
        id
        status
        progressValue
        targetValue
        completionCount
        pointsEarned
        lastCompletedAt
        streakCount
      }
    }
  }
}
```

### Query Community Tasks

```graphql
query GetCommunityTasks {
  taskListByType(input: { taskType: COMMUNITY }) {
    success
    message
    data {
      task {
        id
        name
        description
        points
        externalLink
        verificationMethod
      }
      progress {
        status
        completionCount
        pointsEarned
      }
    }
  }
}
```

### Query Trading Tasks

```graphql
query GetTradingTasks {
  taskListByType(input: { taskType: TRADING }) {
    success
    message
    data {
      task {
        id
        name
        description
        points
        conditions
      }
      progress {
        status
        progressValue
        targetValue
        pointsEarned
      }
    }
  }
}
```

## Response Examples

### Successful Response

```json
{
  "data": {
    "taskListByType": {
      "success": true,
      "message": "Task list retrieved successfully",
      "data": [
        {
          "task": {
            "id": "123e4567-e89b-12d3-a456-426614174000",
            "name": "Daily Check-in",
            "description": "Complete daily check-in to earn points",
            "taskType": "DAILY",
            "frequency": "DAILY",
            "points": 5,
            "isActive": true,
            "sortOrder": 1,
            "category": {
              "id": "1",
              "name": "daily",
              "displayName": "Daily Tasks"
            }
          },
          "progress": {
            "id": "456e7890-e89b-12d3-a456-426614174001",
            "status": "NOT_STARTED",
            "progressValue": 0,
            "targetValue": 1,
            "completionCount": 0,
            "pointsEarned": 0,
            "lastCompletedAt": null,
            "streakCount": 0
          }
        }
      ]
    }
  }
}
```

### Error Response

```json
{
  "data": {
    "taskListByType": {
      "success": false,
      "message": "Invalid task type",
      "data": []
    }
  }
}
```

## Task Status Values

- `NOT_STARTED`: Task has not been started
- `IN_PROGRESS`: Task is currently in progress
- `COMPLETED`: Task has been completed but reward not claimed
- `CLAIMED`: Task reward has been claimed
- `EXPIRED`: Task has expired

## Authentication

This API requires authentication. Include a valid JWT token in the Authorization header:

```
Authorization: Bearer <your-jwt-token>
```

## Error Handling

The API uses a consistent error response format:

- `success`: Boolean indicating if the request was successful
- `message`: Human-readable error message
- `data`: Empty array for error responses

Common error scenarios:
- Invalid or missing authentication token
- Invalid task type parameter
- Database connection issues
- User initialization failures

## Implementation Details

### Service Layer

The API is implemented using the following service methods:

1. `ActivityCashbackService.InitializeUserForActivityCashback()` - Ensures user is initialized
2. `ActivityCashbackService.GetTaskListByType()` - Retrieves tasks by type with progress
3. `TaskManagementService.GetTasksByType()` - Gets tasks filtered by type
4. `TaskProgressService.GetUserTaskProgress()` - Gets user progress for all tasks

### Repository Layer

Uses the following repository methods:

1. `ActivityTaskRepository.GetByTaskType()` - Retrieves tasks by TaskType enum
2. `UserTaskProgressRepository.GetByUserID()` - Gets user progress records

### Performance Considerations

- Tasks are filtered at the database level for efficiency
- User progress is loaded in a single query and mapped in memory
- Results are ordered by `sort_order` and `created_at` for consistent display

## Related APIs

- `taskCenter` - Gets all tasks grouped by category
- `tasksByCategory` - Gets tasks filtered by category name
- `userTaskProgress` - Gets user's task progress only
- `completeTask` - Completes a specific task
- `claimTaskReward` - Claims reward for completed task
