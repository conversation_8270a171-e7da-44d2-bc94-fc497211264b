package resolvers

import (
	"context"
	"testing"

	"github.com/google/uuid"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/mock"
	"gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/controller/graphql/gql_model"
	"gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/model"
	"gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/service/activity_cashback"
)

// MockActivityCashbackService for testing
type MockActivityCashbackService struct {
	mock.Mock
}

func (m *MockActivityCashbackService) InitializeUserForActivityCashback(ctx context.Context, userID uuid.UUID) error {
	args := m.Called(ctx, userID)
	return args.Error(0)
}

func (m *MockActivityCashbackService) GetTaskListByType(ctx context.Context, userID uuid.UUID, taskType model.TaskType) ([]*activity_cashback.TaskWithProgress, error) {
	args := m.Called(ctx, userID, taskType)
	return args.Get(0).([]*activity_cashback.TaskWithProgress), args.Error(1)
}

func TestActivityCashbackResolver_TaskListByType(t *testing.T) {
	// Create mock service
	mockService := &MockActivityCashbackService{}

	// Test data
	userID := uuid.New()
	taskID := uuid.New()
	categoryID := uint(1)

	// Mock task
	mockTask := model.ActivityTask{
		ID:         taskID,
		CategoryID: categoryID,
		Name:       "Daily Check-in",
		TaskType:   model.TaskTypeDaily,
		Points:     5,
		IsActive:   true,
		SortOrder:  1,
		Category: model.TaskCategory{
			ID:          categoryID,
			Name:        "daily",
			DisplayName: "Daily Tasks",
		},
	}

	// Mock progress
	mockProgress := &model.UserTaskProgress{
		ID:              uuid.New(),
		UserID:          userID,
		TaskID:          taskID,
		Status:          model.TaskStatusNotStarted,
		ProgressValue:   0,
		CompletionCount: 0,
		PointsEarned:    0,
		StreakCount:     0,
	}

	// Mock task with progress
	mockTaskWithProgress := &activity_cashback.TaskWithProgress{
		Task:     mockTask,
		Progress: mockProgress,
	}

	// Setup mock expectations
	mockService.On("InitializeUserForActivityCashback", mock.Anything, userID).Return(nil)
	mockService.On("GetTaskListByType", mock.Anything, userID, model.TaskTypeDaily).Return([]*activity_cashback.TaskWithProgress{mockTaskWithProgress}, nil)

	// Create input
	input := gql_model.TaskListByTypeInput{
		TaskType: gql_model.TaskTypeDaily,
	}

	// Note: This test would need to be adjusted to work with the actual service injection
	// For now, we'll just test the structure and logic

	// Test input validation
	assert.Equal(t, gql_model.TaskTypeDaily, input.TaskType)

	// Test GraphQL type conversion
	var taskType model.TaskType
	switch input.TaskType {
	case gql_model.TaskTypeDaily:
		taskType = model.TaskTypeDaily
	case gql_model.TaskTypeCommunity:
		taskType = model.TaskTypeCommunity
	case gql_model.TaskTypeTrading:
		taskType = model.TaskTypeTrading
	}

	assert.Equal(t, model.TaskTypeDaily, taskType)

	// Test conversion function
	gqlTaskWithProgress := convertTaskWithProgressToGQL(mockTaskWithProgress)
	assert.NotNil(t, gqlTaskWithProgress)
	assert.Equal(t, mockTask.Name, gqlTaskWithProgress.Task.Name)
	assert.Equal(t, string(mockProgress.Status), string(gqlTaskWithProgress.Progress.Status))
}

func TestTaskListByType_InvalidTaskType(t *testing.T) {
	// Test that invalid task type is handled properly
	input := gql_model.TaskListByTypeInput{
		TaskType: "INVALID", // This would be caught by GraphQL validation
	}

	// This test demonstrates that GraphQL enum validation would catch invalid types
	// before reaching the resolver
	assert.NotEqual(t, gql_model.TaskTypeDaily, input.TaskType)
}

func TestTaskListByType_ConversionFunctions(t *testing.T) {
	// Test the conversion from service types to GraphQL types
	taskID := uuid.New()
	userID := uuid.New()

	// Create service task
	serviceTask := model.ActivityTask{
		ID:       taskID,
		Name:     "Test Task",
		TaskType: model.TaskTypeDaily,
		Points:   10,
	}

	// Create service progress
	serviceProgress := &model.UserTaskProgress{
		ID:     uuid.New(),
		UserID: userID,
		TaskID: taskID,
		Status: model.TaskStatusCompleted,
	}

	// Create service TaskWithProgress
	serviceTaskWithProgress := &activity_cashback.TaskWithProgress{
		Task:     serviceTask,
		Progress: serviceProgress,
	}

	// Convert to GraphQL type
	gqlTaskWithProgress := convertTaskWithProgressToGQL(serviceTaskWithProgress)

	// Verify conversion
	assert.NotNil(t, gqlTaskWithProgress)
	assert.Equal(t, serviceTask.Name, gqlTaskWithProgress.Task.Name)
	assert.Equal(t, string(serviceProgress.Status), string(gqlTaskWithProgress.Progress.Status))
}
